#!/usr/bin/env python3
"""
最终综合分析 - 从所有可能的角度寻找flag
"""

import requests
import hashlib
import json
import time
import re
import base64
from urllib.parse import urljoin

class FinalAnalyzer:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
        })
        self.found_flags = []
        
    def log(self, message, level="INFO"):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def search_for_flags(self, text, source=""):
        """搜索flag"""
        if not text:
            return []
            
        flag_patterns = [
            r'flag\{[^}]+\}',
            r'FLAG\{[^}]+\}',
            r'ctf\{[^}]+\}',
            r'CTF\{[^}]+\}',
            r'[a-zA-Z0-9]{32}',
            r'[a-zA-Z0-9]{40}',
            r'[a-zA-Z0-9_-]{20,50}',
            r'flag[:\s]*[a-zA-Z0-9_-]+',
            r'key[:\s]*[a-zA-Z0-9_-]+',
            r'answer[:\s]*[a-zA-Z0-9_-]+',
            r'secret[:\s]*[a-zA-Z0-9_-]+',
        ]
        
        found = []
        for pattern in flag_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) > 5:
                    found.append(match)
                    
        if found:
            self.log(f"🎯 在{source}中发现潜在flag: {found}", "FLAG")
            self.found_flags.extend(found)
            
        return found
    
    def analyze_all_possible_sources(self):
        """分析所有可能的flag来源"""
        self.log("🔍 分析所有可能的flag来源...")
        
        # 1. 分析前端HTML源码
        self.analyze_frontend_source()
        
        # 2. 分析robots.txt和其他公开文件
        self.analyze_public_files()
        
        # 3. 分析HTTP响应头
        self.analyze_response_headers()
        
        # 4. 分析错误消息
        self.analyze_error_messages()
        
        # 5. 分析JavaScript代码
        self.analyze_javascript_code()
        
        # 6. 尝试目录遍历
        self.attempt_directory_traversal()
        
        # 7. 分析API响应
        self.analyze_api_responses()
    
    def analyze_frontend_source(self):
        """分析前端HTML源码"""
        self.log("📄 分析前端HTML源码...")
        
        try:
            # 读取本地的admin.html文件
            with open('admin.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
                
            self.search_for_flags(html_content, "HTML源码")
            
            # 查找注释中的信息
            comment_pattern = r'<!--(.*?)-->'
            comments = re.findall(comment_pattern, html_content, re.DOTALL)
            for comment in comments:
                self.search_for_flags(comment, "HTML注释")
                
            # 查找JavaScript中的字符串
            js_string_pattern = r'["\']([^"\']{10,})["\']'
            js_strings = re.findall(js_string_pattern, html_content)
            for js_string in js_strings:
                self.search_for_flags(js_string, "JavaScript字符串")
                
        except Exception as e:
            self.log(f"分析HTML源码失败: {e}")
    
    def analyze_public_files(self):
        """分析公开文件"""
        self.log("🌐 分析公开文件...")
        
        public_files = [
            '/robots.txt',
            '/sitemap.xml',
            '/favicon.ico',
            '/.well-known/security.txt',
            '/flag.txt',
            '/flag',
            '/secret.txt',
            '/admin.txt',
            '/readme.txt',
            '/info.txt',
            '/test.txt',
            '/debug.txt',
            '/config.txt',
            '/backup.txt',
            '/.env',
            '/config.json',
            '/package.json',
            '/manifest.json'
        ]
        
        for file_path in public_files:
            try:
                response = self.session.get(urljoin(self.base_url, file_path), timeout=10)
                if response.status_code == 200:
                    self.log(f"✅ 发现公开文件: {file_path}")
                    self.search_for_flags(response.text, f"公开文件({file_path})")
                    
            except Exception as e:
                pass
    
    def analyze_response_headers(self):
        """分析HTTP响应头"""
        self.log("📡 分析HTTP响应头...")
        
        test_urls = [
            '/',
            '/admin/login',
            '/admin/questions',
            '/robots.txt'
        ]
        
        for url in test_urls:
            try:
                response = self.session.get(urljoin(self.base_url, url), timeout=10)
                
                # 检查所有响应头
                for header, value in response.headers.items():
                    self.search_for_flags(value, f"响应头({header})")
                    
                    # 特别检查一些可能包含flag的头
                    if any(keyword in header.lower() for keyword in ['flag', 'secret', 'key', 'token', 'hint']):
                        self.log(f"🎯 有趣的响应头: {header}: {value}")
                        
            except Exception as e:
                pass
    
    def analyze_error_messages(self):
        """分析错误消息"""
        self.log("❌ 分析错误消息...")
        
        # 尝试各种错误情况
        error_tests = [
            ('/admin/login', 'POST', {'email': 'test', 'password': 'test'}),
            ('/admin/questions', 'GET', None),
            ('/admin/nonexistent', 'GET', None),
            ('/admin/login', 'POST', {'invalid': 'data'}),
            ('/admin/login', 'POST', {'email': '<EMAIL>'}),  # 缺少密码
        ]
        
        for url, method, data in error_tests:
            try:
                if method == 'POST':
                    response = self.session.post(urljoin(self.base_url, url), json=data, timeout=10)
                else:
                    response = self.session.get(urljoin(self.base_url, url), timeout=10)
                    
                self.search_for_flags(response.text, f"错误响应({url})")
                
            except Exception as e:
                pass
    
    def analyze_javascript_code(self):
        """分析JavaScript代码"""
        self.log("🔧 分析JavaScript代码...")
        
        try:
            with open('admin.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
                
            # 提取所有JavaScript代码
            script_pattern = r'<script[^>]*>(.*?)</script>'
            scripts = re.findall(script_pattern, html_content, re.DOTALL)
            
            for script in scripts:
                self.search_for_flags(script, "JavaScript代码")
                
                # 查找可能的硬编码凭据
                credential_patterns = [
                    r'password["\s]*[:=]["\s]*([^"\';\s]+)',
                    r'email["\s]*[:=]["\s]*([^"\';\s]+)',
                    r'admin["\s]*[:=]["\s]*([^"\';\s]+)',
                    r'secret["\s]*[:=]["\s]*([^"\';\s]+)',
                    r'key["\s]*[:=]["\s]*([^"\';\s]+)',
                ]
                
                for pattern in credential_patterns:
                    matches = re.findall(pattern, script, re.IGNORECASE)
                    for match in matches:
                        self.log(f"🔑 发现可能的凭据: {match}")
                        
        except Exception as e:
            self.log(f"分析JavaScript失败: {e}")
    
    def attempt_directory_traversal(self):
        """尝试目录遍历"""
        self.log("📂 尝试目录遍历...")
        
        common_directories = [
            '/admin/',
            '/api/',
            '/test/',
            '/debug/',
            '/config/',
            '/backup/',
            '/tmp/',
            '/uploads/',
            '/files/',
            '/data/',
            '/logs/',
            '/static/',
            '/assets/',
            '/public/',
            '/private/',
            '/.git/',
            '/.svn/',
            '/node_modules/',
        ]
        
        for directory in common_directories:
            try:
                response = self.session.get(urljoin(self.base_url, directory), timeout=10)
                if response.status_code == 200:
                    self.log(f"✅ 发现目录: {directory}")
                    self.search_for_flags(response.text, f"目录({directory})")
                    
            except Exception as e:
                pass
    
    def analyze_api_responses(self):
        """分析API响应"""
        self.log("🔌 分析API响应...")
        
        # 尝试各种API端点
        api_endpoints = [
            '/api/status',
            '/api/health',
            '/api/info',
            '/api/version',
            '/api/config',
            '/api/debug',
            '/status',
            '/health',
            '/info',
            '/version',
            '/ping',
            '/test',
        ]
        
        for endpoint in api_endpoints:
            try:
                response = self.session.get(urljoin(self.base_url, endpoint), timeout=10)
                if response.status_code == 200:
                    self.log(f"✅ 发现API端点: {endpoint}")
                    self.search_for_flags(response.text, f"API({endpoint})")
                    
            except Exception as e:
                pass
    
    def check_base64_encoded_content(self):
        """检查base64编码的内容"""
        self.log("🔤 检查base64编码内容...")
        
        # 从各种来源收集可能的base64字符串
        potential_base64 = []
        
        # 从HTML中提取
        try:
            with open('admin.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
                
            base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
            matches = re.findall(base64_pattern, html_content)
            potential_base64.extend(matches)
            
        except Exception as e:
            pass
        
        # 尝试解码
        for b64_string in potential_base64:
            try:
                decoded = base64.b64decode(b64_string).decode('utf-8', errors='ignore')
                if len(decoded) > 5:
                    self.log(f"🔓 Base64解码: {b64_string} -> {decoded}")
                    self.search_for_flags(decoded, "Base64解码")
                    
            except Exception as e:
                pass
    
    def run_final_analysis(self):
        """运行最终分析"""
        print("🎯 最终综合分析 - 从所有角度寻找flag")
        print(f"目标: {self.base_url}")
        print("=" * 70)
        
        # 执行所有分析
        self.analyze_all_possible_sources()
        self.check_base64_encoded_content()
        
        # 生成最终报告
        self.generate_final_report()
    
    def generate_final_report(self):
        """生成最终报告"""
        print("\n" + "="*70)
        print("🏆 最终分析结果")
        print("="*70)
        
        if self.found_flags:
            print(f"🎉 发现 {len(self.found_flags)} 个潜在flag:")
            for i, flag in enumerate(self.found_flags, 1):
                print(f"{i}. {flag}")
        else:
            print("❌ 未发现明显的flag")
            
        print("\n🎯 总结:")
        print("1. 已尝试所有常见的登录凭据组合")
        print("2. 已分析前端源码和JavaScript")
        print("3. 已检查公开文件和目录")
        print("4. 已分析HTTP响应头和错误消息")
        print("5. 已尝试各种注入和绕过技术")
        
        print("\n💡 可能的下一步:")
        print("1. 这可能是一个需要特定知识或线索的挑战")
        print("2. Flag可能隐藏在我们还没有发现的地方")
        print("3. 可能需要社会工程学或其他非技术手段")
        print("4. 检查是否有其他相关的服务或域名")

if __name__ == "__main__":
    target_url = "https://problem-test-api.mr-onion-blog.fun"
    
    analyzer = FinalAnalyzer(target_url)
    analyzer.run_final_analysis()
