{"target": "https://problem-test-api.mr-onion-blog.fun", "timestamp": "2025-08-14 15:34:43", "vulnerabilities_count": 6, "vulnerabilities": [{"type": "未授权访问", "description": "端点 /admin/questions 使用 OPTIONS 方法可以未授权访问", "severity": "HIGH", "proof": "响应: ", "timestamp": "2025-08-14 15:34:27"}, {"type": "未授权访问", "description": "端点 /admin/questions?status=all 使用 OPTIONS 方法可以未授权访问", "severity": "HIGH", "proof": "响应: ", "timestamp": "2025-08-14 15:34:29"}, {"type": "未授权访问", "description": "端点 /admin/approve/test123 使用 OPTIONS 方法可以未授权访问", "severity": "HIGH", "proof": "响应: ", "timestamp": "2025-08-14 15:34:30"}, {"type": "未授权访问", "description": "端点 /admin/reject/test123 使用 OPTIONS 方法可以未授权访问", "severity": "HIGH", "proof": "响应: ", "timestamp": "2025-08-14 15:34:32"}, {"type": "未授权访问", "description": "端点 /admin/download/description/test123 使用 OPTIONS 方法可以未授权访问", "severity": "HIGH", "proof": "响应: ", "timestamp": "2025-08-14 15:34:33"}, {"type": "未授权访问", "description": "端点 /admin/download/data/test123 使用 OPTIONS 方法可以未授权访问", "severity": "HIGH", "proof": "响应: ", "timestamp": "2025-08-14 15:34:35"}]}